import Button from "@/components/Buttons/Button";
import { APIROUTES } from "@/helpers/apiRoutes";
import { handlePost } from "@/helpers/axiosInstance";
import { handleLogout } from "@/helpers/handleLogout";
import { orpc } from "@/lib/orpc";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../../app/store/stores";

const GeneralPageSection = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const setAuthed = useAuthStore((state) => state.setAuthed);

    const addXP = async () => {
        try {
            await handlePost("/dev/addxp", { xp: 2000 });
            setTimeout(
                () =>
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    }),
                100
            );
        } catch (error) {
            console.error("Error adding XP:", error);
        }
    };

    const addCash = async () => {
        try {
            await handlePost("/dev/addcash", {});
            setTimeout(
                () =>
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    }),
                100
            );
        } catch (error) {
            console.error("Error adding cash:", error);
        }
    };

    const addStats = async () => {
        try {
            await handlePost("/dev/addstats", {});
            setTimeout(
                () =>
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    }),
                100
            );
        } catch (error) {
            console.error("Error adding stats:", error);
        }
    };

    const removeStats = async () => {
        try {
            await handlePost("/dev/removestats", {});
            setTimeout(
                () =>
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    }),
                100
            );
        } catch (error) {
            console.error("Error removing stats:", error);
        }
    };

    const resetQuests = async () => {
        try {
            await handlePost("/dev/resetquests", {});
            setTimeout(() => queryClient.invalidateQueries({ queryKey: orpc.quest.getCombinedList.key() }), 100);
            setTimeout(() => queryClient.invalidateQueries({ queryKey: orpc.quest.getAvailable.key() }), 100);
        } catch (error) {
            console.error("Error resetting quests:", error);
        }
    };

    const completeQuests = async () => {
        try {
            await handlePost("/dev/completequests", {});
            setTimeout(() => queryClient.invalidateQueries({ queryKey: orpc.quest.getCombinedList.key() }), 100);
            setTimeout(() => queryClient.invalidateQueries({ queryKey: orpc.quest.getAvailable.key() }), 100);
        } catch (error) {
            console.error("Error completing quests:", error);
        }
    };

    const login = async (email) => {
        try {
            const response = await handlePost(APIROUTES.AUTH.LOGIN, {
                email: email,
                password: "testtest123",
            });

            setAuthed(true);
            navigate("/home");
        } catch (error) {
            console.error("Login error:", error);
        }
    };

    const createTestUser = async () => {
        try {
            const response = await handlePost("/admin/create-test-user", {});

            await handleLogout();
            setTimeout(() => {
                login(response.email);
            }, 500);
        } catch (error) {
            console.error("Error creating test user:", error);
        }
    };

    const randomMap = async () => {
        try {
            await handlePost("/dev/randomroguelike", {});

            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
            setTimeout(() => queryClient.invalidateQueries({ queryKey: APIROUTES.ROGUELIKE.CURRENTMAP }), 100);
            navigate("/streets");
        } catch (error) {
            console.error("Error generating random map:", error);
        }
    };

    const allItems = async () => {
        try {
            await handlePost("/dev/addallitems", {});
            queryClient.invalidateQueries({ queryKey: APIROUTES.USER.INVENTORY });
        } catch (error) {
            console.error("Error adding all items:", error);
        }
    };

    const resetExploreNodes = async () => {
        try {
            await handlePost("/dev/deleteexplorenodes", {});
            queryClient.invalidateQueries({ queryKey: orpc.explore.getMapByLocation.key() });
        } catch (error) {
            console.error("Error resetting explore nodes:", error);
        }
    };

    return (
        <div className="grid grid-cols-2 gap-3 p-2">
            <Button className="text-sm!" type="primary" onClick={addXP}>
                Gain 2000 EXP
            </Button>
            <Button className="text-sm!" type="primary" onClick={addCash}>
                +5k Cash
            </Button>
            <Button className="text-sm!" type="primary" onClick={addStats}>
                +200 Stats
            </Button>
            <Button className="text-sm!" type="primary" onClick={removeStats}>
                -200 Stats
            </Button>
            <Button className="text-sm!" type="primary" onClick={resetQuests}>
                Reset Quests
            </Button>
            <Button className="text-sm!" type="primary" onClick={randomMap}>
                Random Map
            </Button>
            <Button className="text-sm!" type="primary" onClick={allItems}>
                Add All Items
            </Button>
            <Button className="text-sm!" type="primary" onClick={completeQuests}>
                Complete Quests
            </Button>
            <Button className="text-sm!" type="primary" onClick={createTestUser}>
                Create Test User
            </Button>
            <Button className="text-sm!" type="primary" onClick={resetExploreNodes}>
                Reset Explore Nodes
            </Button>
        </div>
    );
};

export default GeneralPageSection;
