import Button from "@/components/Buttons/Button";
import useDrag from "@/hooks/useDrag";
import { cn } from "@/lib/utils";
import { useState } from "react";
import CombatPageSection from "./CombatPageSection";
import GeneralPageSection from "./GeneralPageSection";
import ItemsPageSection from "./ItemsPageSection";
import PetsPageSection from "./PetsPageSection";

const DevMenu = ({ setShowDevMenu }) => {
    const [page, setPage] = useState("general");
    const [ref] = useDrag();

    return (
        <div
            ref={ref}
            className={cn(
                "-translate-x-1/2 fixed bottom-16 left-1 z-999 flex h-fit select-none flex-col rounded-md border-4 border-green-500 bg-gray-800 text-white"
            )}
        >
            <div className="mx-auto flex w-full cursor-move border-green-500 border-b bg-black py-2 text-center text-xl">
                <p className="mx-auto select-none text-center">Dev Menu</p>
                <div className="absolute right-3 cursor-pointer" onClick={() => setShowDevMenu(false)}>
                    X
                </div>
            </div>
            <div className="flex gap-2 border-white border-b p-2">
                <Button type="secondary" onClick={() => setPage("general")}>
                    General
                </Button>
                <Button type="secondary" onClick={() => setPage("combat")}>
                    Combat
                </Button>
                <Button type="secondary" onClick={() => setPage("items")}>
                    Items
                </Button>
                <Button type="secondary" onClick={() => setPage("pets")}>
                    Pets
                </Button>
            </div>
            <div className="min-h-80 min-w-64">
                {page === "general" ? <GeneralPageSection /> : null}
                {page === "combat" ? <CombatPageSection /> : null}
                {page === "items" ? <ItemsPageSection /> : null}
                {page === "pets" ? <PetsPageSection /> : null}
            </div>
        </div>
    );
};

export default DevMenu;
