import type { Express } from "express";
import { uniqueItemsConfig } from "../../config/gameConfig.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as GangHelper from "./gang.helpers.js";
import * as GangRepository from "../../repositories/gang.repository.js";
import { NotificationTypes } from "../../types/notification.js";
import * as imagesHelper from "../../utils/images.js";
import { Prisma, type gang as GangModel } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";

const { GANG_CREATION_ITEM_NAME } = uniqueItemsConfig.public;

interface PayoutShare {
    userId: number;
    payoutShare: number;
}

let cachedGangCreationItemId: number | null = null;

// const gangRanks = {
//     LEADER: "leader",
//     LT: "lieutenant",
//     OFFICER: "officer",
//     THUG: "thug",
//     MEMBER: "member",
//     ROOKIE: "rookie",
// } as const;

async function cacheGangCreationItemId(): Promise<number | null> {
    const item = await ItemRepository.findItemByName(GANG_CREATION_ITEM_NAME);
    if (item) {
        cachedGangCreationItemId = item.id;
    }
    return cachedGangCreationItemId;
}

export async function gangList(userId: number) {
    const gangs = await GangRepository.findAllGangs(userId);
    return { data: gangs };
}

export async function getGangInviteList(gangId: number) {
    const invites = await GangRepository.findGangInvites(gangId);
    return { data: invites };
}

export async function getGangInfo(gangId: number) {
    const gang = await GangRepository.findGangById(gangId);
    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }
    return { data: gang };
}

export async function getUserInviteList(userId: number) {
    const invites = await GangRepository.findUserInvites(userId);
    return { data: invites };
}

export async function getUserInviteRequestList(userId: number) {
    const invites = await GangRepository.findUserInviteRequests(userId);
    return { data: invites };
}

export async function hasGangItem(userId: number) {
    const gangCreationItemId = cachedGangCreationItemId ?? (await cacheGangCreationItemId());
    if (!gangCreationItemId || !(await InventoryService.UserHasItem(userId, gangCreationItemId))) {
        return { data: false };
    }
    return { data: true };
}

export async function getGangLogs(userId: number, gangId: number) {
    gangId = Number.parseInt(String(gangId));
    if (!gangId) {
        return { error: "No gang id provided!", statusCode: 400 };
    }

    const user = await GangRepository.findUserWithGangAndType(userId);
    if (user && user.gangId !== gangId && user.userType !== "admin") {
        return { error: "You need to be in the gang you want to view logs for!", statusCode: 400 };
    }

    const gang = await GangRepository.findGangById(gangId);
    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const gangLogs = await GangRepository.findGangLogs(gangId);
    return { data: gangLogs };
}

export async function getGangMemberShares(userId: number, gangId: number) {
    gangId = Number.parseInt(String(gangId));
    if (!gangId) {
        return { error: "No gang id provided!", statusCode: 400 };
    }

    const user = await GangRepository.findUserWithGangAndType(userId);
    if (user && user.gangId !== gangId && user.userType !== "admin") {
        return { error: "You need to be in the gang you want to view logs for!", statusCode: 400 };
    }

    const gangs = await GangRepository.findAllGangsRaw();
    const selectedGang = gangs.find((g) => g.id === gangId);
    if (!selectedGang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const gangRankingMultiplier = GangHelper.GetGangRankingMultiplier(gangs, selectedGang);
    const gangMembers = await GangRepository.findGangMembers(selectedGang.id);
    if (!gangMembers) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const totalPool = GangHelper.CalculateGangWeeklyEarnings(gangMembers, gangRankingMultiplier);
    const shares = GangHelper.CalculateGangPayoutShares(gangMembers, totalPool);
    const memberShares = shares.map((member) => ({
        userId: member.userId,
        payoutShare: member.payoutPercentage,
    }));

    return { data: { gangRankingMultiplier, memberShares } };
}

export async function currentGangDetails(userId: number) {
    const userGang = await GangRepository.findUserWithGangId(userId);
    if (!userGang || !userGang.gangId) {
        return { error: "No gang found!", statusCode: 404 };
    }

    const gang = await GangRepository.findCurrentGangDetails(userGang.gangId);
    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const currentUserRank = await GangRepository.findGangMemberWithRank(userId, gang.id);
    // Create a new object with the additional property to avoid TypeScript error
    const gangWithUserRank = {
        ...gang,
        currentUserRank: currentUserRank ? currentUserRank.rank : null,
    };

    return { data: gangWithUserRank };
}

export async function createGang(userId: number, body: Prisma.gangCreateInput) {
    let gang: GangModel | null = null;

    const gangCreationItemId = cachedGangCreationItemId ?? (await cacheGangCreationItemId());
    if (!gangCreationItemId || !(await InventoryService.UserHasItem(userId, gangCreationItemId))) {
        return { error: "Missing Gang creation item", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);

    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }

    if (currentUser.gangId != null) {
        return { error: "User is already in a gang", statusCode: 400 };
    }

    if (!body.name) {
        return { error: "Missing required fields", statusCode: 400 };
    }

    // Validate gang name length
    const gangName = body.name.trim();
    if (gangName.length < 4 || gangName.length > 40) {
        return { error: "Gang name must be between 4 and 40 characters", statusCode: 400 };
    }

    // Capitalize the first letter of each word
    const capitalizedGangName = gangName
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");

    const existingGang = await GangRepository.findGangByName(capitalizedGangName);
    if (existingGang != null) {
        return { error: "Gang name already exists", statusCode: 400 };
    }

    const data = {
        name: capitalizedGangName,
        about: body.about,
    };

    // TODO: should probably be a database transaction
    await InventoryService.SubtractItemFromUser({ userId: currentUser.id, itemId: gangCreationItemId, amount: 1 });
    gang = await GangRepository.createGang(data);
    await GangRepository.setGangOwner(gang, currentUser);
    await GangRepository.setUserGang(currentUser, gang);
    await GangRepository.createGangMember({
        user: { connect: { id: currentUser.id } },
        gang: { connect: { id: gang.id } },
        rank: 6,
        payoutShare: 100,
    });
    GangHelper.logGangAction(gang.id, "GangCreated", "", userId, null);

    return { data: gang };
}

const getMemberCapacity = (gang: { hideout_level: number | null }) => {
    // -- TEMP FOR ALPHA --
    if (gang.hideout_level === 0) {
        return 5;
    }
    return 7;
    // -- TEMP FOR ALPHA --
    return 5 + (gang.hideout_level ?? 0) * 3;
};

export async function inviteToGang(userId: number, gangId: number | null, targetUserId: number) {
    if (!gangId) {
        return { error: "You are not in a gang", statusCode: 400 };
    }

    const gang = await GangRepository.findGangById(gangId);
    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const user = await UserRepository.getUserById(targetUserId);
    if (!user) {
        return { error: "User not found", statusCode: 404 };
    }

    if (user.gangId) {
        return { error: "User is already in a gang", statusCode: 400 };
    }

    const gangMember = await GangRepository.findGangMember(userId, gangId);
    if (gangMember && gangMember.rank < 3) {
        return { error: "Your rank too low to invite player to gang", statusCode: 400 };
    }

    const currentMembersCount = await GangRepository.countGangMembers(gangId);
    if (gang && currentMembersCount >= getMemberCapacity(gang)) {
        return { error: "Gang is at full capacity", statusCode: 400 };
    }

    const existingInvite = await GangRepository.findGangInvite(gangId, targetUserId, "invite");
    const existingApplication = await GangRepository.findGangInvite(gangId, targetUserId, "inviteRequest");

    if (existingInvite) {
        return { error: "An invite to this gang has already been sent to this user.", statusCode: 400 };
    }

    if (existingApplication) {
        await GangRepository.updateGangInvite(existingApplication, {
            inviteType: "invite",
            user_gang_invite_senderIdTouser: { connect: { id: userId } },
            user_gang_invite_recipientIdTouser: { connect: { id: targetUserId } },
        });
    } else {
        await GangRepository.createGangInvite({
            gang: { connect: { id: gangId } },
            user_gang_invite_senderIdTouser: { connect: { id: userId } },
            user_gang_invite_recipientIdTouser: { connect: { id: targetUserId } },
            inviteType: "invite",
        });
    }

    GangHelper.logGangAction(gangId, "PlayerInvited", "", userId, targetUserId);
    NotificationService.NotifyUser(targetUserId, NotificationTypes.gang_invite, {
        gangId: gangId,
        gangName: gang.name,
        senderId: userId,
    });

    return { data: "Invitation sent" };
}

export async function acceptGangInvite(userId: number, inviteId: number) {
    const invite = await GangRepository.findGangInviteByPk(inviteId);
    if (!invite) {
        return { error: "Invite not found", statusCode: 404 };
    }

    if (!invite.gangId) {
        return { error: "Invalid gang id on invite", statusCode: 400 };
    }

    const gang = await GangRepository.findGangById(invite.gangId);
    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const user = await UserRepository.getUserById(userId);
    if (!user) {
        return { error: "User not found", statusCode: 404 };
    }

    if (user.gangId) {
        return { error: "User is already in a gang", statusCode: 400 };
    }

    const currentMembersCount = await GangRepository.countGangMembers(gang.id);
    if (currentMembersCount >= getMemberCapacity(gang)) {
        return { error: "Gang is at full capacity", statusCode: 400 };
    }

    if (invite.recipientId !== userId) {
        return { error: "This invite is not for you", statusCode: 400 };
    }

    await GangRepository.updateUser({ id: user.id }, { gang: { connect: { id: gang.id } } });
    await GangRepository.createGangMember({
        user: { connect: { id: userId } },
        gang: { connect: { id: gang.id } },
        rank: 1,
        payoutShare: 0,
    });
    await GangRepository.destroyGangInvite({ id: inviteId });

    GangHelper.logGangAction(gang.id, "PlayerJoined", "", userId, null);

    return { data: "Successfully joined gang" };
}

export async function declineGangInvite(userId: number, inviteId: number) {
    const invite = await GangRepository.findGangInviteByPk(inviteId);
    if (!invite) {
        return { error: "Invite not found", statusCode: 404 };
    }

    if (invite.recipientId !== userId) {
        return { error: "This invite is not for you", statusCode: 400 };
    }

    await GangRepository.destroyGangInvite({ id: inviteId });
    return { data: "Invitation declined" };
}

interface AssignRankBody {
    targetUserId: number;
    rank: number;
}

export async function assignGangRank(userId: number, body: AssignRankBody) {
    try {
        const { targetUserId, rank } = body;

        if (!targetUserId || !rank) {
            return { error: "Missing required fields", statusCode: 400 };
        }

        const user = await GangRepository.findUserWithGangId(userId);
        if (!user || !user.gangId) {
            return { error: "You are not in a gang", statusCode: 400 };
        }
        const gangId = user.gangId;

        const targetUser = await GangRepository.findUserWithGangAndUsername(targetUserId);
        if (!targetUser) {
            return { error: "Target user not found", statusCode: 404 };
        }

        if (targetUser.gangId !== gangId) {
            return { error: "Target user is not in your gang", statusCode: 400 };
        }

        const userGangMember = await GangRepository.findGangMember(userId, gangId);
        if (!userGangMember) {
            return { error: "You are not a member of this gang", statusCode: 400 };
        }

        if (userGangMember.rank < 5) {
            return { error: "You don't have permission to assign ranks", statusCode: 400 };
        }

        if (userGangMember.rank === 5 && rank >= 5) {
            return { error: "You can only assign ranks lower than your own", statusCode: 400 };
        }

        const targetGangMember = await GangRepository.findGangMember(targetUserId, gangId);
        if (!targetGangMember) {
            return { error: "Target user is not a member of this gang", statusCode: 400 };
        }

        if (targetGangMember.rank === 6) {
            return { error: "Cannot change the rank of the gang leader", statusCode: 400 };
        }

        await GangRepository.updateGangMember({ rank }, { id: targetGangMember.id });

        GangHelper.logGangAction(
            gangId,
            "RankChanged",
            `${targetUser.username} rank changed to ${rank}`,
            userId,
            targetUserId
        );

        return { data: "Rank assigned successfully" };
    } catch (error) {
        return { error: "Failed to assign rank", statusCode: 500 };
    }
}

export async function kickMember(userId: number, targetUserId: number) {
    try {
        const user = await GangRepository.findUserWithGangId(userId);
        if (!user || !user.gangId) {
            return { error: "You are not in a gang", statusCode: 400 };
        }
        const gangId = user.gangId;

        const userGangMember = await GangRepository.findGangMember(userId, gangId);
        if (!userGangMember) {
            return { error: "You are not a member of this gang", statusCode: 400 };
        }

        if (userGangMember.rank < 5) {
            return { error: "You don't have permission to kick members", statusCode: 400 };
        }

        const targetUser = await GangRepository.findUserWithGangAndUsername(targetUserId);
        if (!targetUser) {
            return { error: "Target user not found", statusCode: 404 };
        }

        if (targetUser.gangId !== gangId) {
            return { error: "Target user is not in your gang", statusCode: 400 };
        }

        const gang = await GangRepository.findGangById(gangId);
        if (!gang) {
            return { error: "Gang not found", statusCode: 404 };
        }

        if (gang.ownerId === targetUserId) {
            return { error: "Cannot kick the gang owner", statusCode: 400 };
        }

        const targetGangMember = await GangRepository.findGangMember(targetUserId, gangId);
        if (!targetGangMember) {
            return { error: "Target user is not a member of this gang", statusCode: 400 };
        }

        if (targetGangMember.rank >= userGangMember.rank) {
            return { error: "Cannot kick a member with equal or higher rank", statusCode: 400 };
        }

        await GangRepository.updateUser({ id: targetUser.id }, { gang: { disconnect: true } });

        await GangRepository.destroyGangMember({ id: targetGangMember.id });

        GangHelper.logGangAction(
            gangId,
            "MemberKicked",
            `${targetUser.username} was kicked from the gang`,
            userId,
            targetUserId
        );

        NotificationService.NotifyUser(targetUserId, NotificationTypes.gang_kicked, {
            gangId,
            gangName: gang.name,
            kickedBy: userId,
        });

        return { data: "Member kicked successfully" };
    } catch (error) {
        return { error: "Failed to kick member", statusCode: 500 };
    }
}

export async function leaveGang(userId: number) {
    try {
        const user = await GangRepository.findUserWithGangId(userId);
        if (!user || !user.gangId) {
            return { error: "You are not in a gang", statusCode: 400 };
        }
        const gangId = user.gangId;

        const gang = await GangRepository.findGangById(gangId);
        if (!gang) {
            return { error: "Gang not found", statusCode: 404 };
        }

        if (gang.ownerId === userId) {
            return {
                error: "Gang owner cannot leave the gang. Transfer ownership first or disband the gang.",
                statusCode: 400,
            };
        }

        await GangRepository.updateUser({ id: user.id }, { gang: { disconnect: true } });

        const member = await GangRepository.findGangMember(userId, gang.id);
        if (member) {
            await GangRepository.destroyGangMember({ id: member.id });
        }

        GangHelper.logGangAction(gang.id, "MemberLeft", `Member left the gang`, userId, null);

        return { data: "Successfully left the gang" };
    } catch (error) {
        return { error: "Failed to leave gang", statusCode: 500 };
    }
}

interface UpdatePayoutSharesBody {
    shares: PayoutShare[];
}

export async function updatePayoutShares(userId: number, body: UpdatePayoutSharesBody) {
    try {
        const { shares } = body;

        if (!shares || !Array.isArray(shares)) {
            return { error: "Invalid shares data", statusCode: 400 };
        }

        const user = await GangRepository.findUserWithGangId(userId);
        if (!user || !user.gangId) {
            return { error: "You are not in a gang", statusCode: 400 };
        }
        const gangId = user.gangId;

        const userGangMember = await GangRepository.findGangMember(userId, gangId);
        if (!userGangMember) {
            return { error: "You are not a member of this gang", statusCode: 400 };
        }

        if (userGangMember.rank < 5) {
            return { error: "You don't have permission to update payout shares", statusCode: 400 };
        }

        for (const share of shares) {
            const { userId: memberId, payoutShare } = share;

            if (typeof payoutShare !== "number" || payoutShare < 0) {
                return { error: "Invalid payout share value", statusCode: 400 };
            }

            const memberExists = await GangRepository.findGangMember(memberId, gangId);
            if (!memberExists) {
                return { error: `User ${memberId} is not a member of this gang`, statusCode: 400 };
            }
        }

        for (const share of shares) {
            const { userId: memberId, payoutShare } = share;
            const member = await GangRepository.findGangMember(memberId, gangId);
            if (member) {
                await GangRepository.updateGangMember({ payoutShare }, { id: member.id });
            }
        }

        GangHelper.logGangAction(gangId, "PayoutSharesUpdated", "Payout shares have been updated", userId, null);

        return { data: "Payout shares updated successfully" };
    } catch (error) {
        return { error: "Failed to update payout shares", statusCode: 500 };
    }
}

// interface HideoutRequirement {
//     materials: number;
//     essence: number;
//     tools: number;
//     members: number;
// }

// const hideoutRequirements: Record<number, HideoutRequirement> = {
//     1: {
//         materials: 150,
//         essence: 100,
//         tools: 0,
//         members: 3,
//     },
//     2: {
//         materials: 400,
//         essence: 250,
//         tools: 150,
//         members: 5,
//     },
//     3: {
//         materials: 700,
//         essence: 500,
//         tools: 300,
//         members: 7,
//     },
// };

export async function upgradeHideout(userId: number) {
    try {
        const user = await GangRepository.findUserWithGangId(userId);
        if (!user || !user.gangId) {
            return { error: "You are not in a gang", statusCode: 400 };
        }
        const gangId = user.gangId;

        const userGangMember = await GangRepository.findGangMember(userId, gangId);
        if (!userGangMember) {
            return { error: "You are not a member of this gang", statusCode: 400 };
        }

        if (userGangMember.rank < 5) {
            return { error: "You don't have permission to upgrade hideout", statusCode: 400 };
        }

        const gang = await GangRepository.findGangWithResources(gangId);
        if (!gang) {
            return { error: "Gang not found", statusCode: 404 };
        }

        const hideoutLevel = gang.hideout_level ?? 0;
        const treasuryBalance = gang.treasury_balance ?? 0;

        const maxHideoutLevel = 10;
        if (hideoutLevel >= maxHideoutLevel) {
            return { error: "Hideout is already at maximum level", statusCode: 400 };
        }

        const upgradeCost = 1000 * (hideoutLevel + 1);
        if (treasuryBalance < upgradeCost) {
            return { error: "Not enough resources to upgrade hideout", statusCode: 400 };
        }

        await GangRepository.updateGangResources(gang, {
            treasury_balance: treasuryBalance - upgradeCost,
            hideout_level: hideoutLevel + 1,
        });

        GangHelper.logGangAction(
            gangId,
            "HideoutUpgraded",
            `Hideout upgraded to level ${hideoutLevel + 1}`,
            userId,
            null
        );

        return { data: { newLevel: hideoutLevel + 1, remainingBalance: treasuryBalance - upgradeCost } };
    } catch (error) {
        return { error: "Failed to upgrade hideout", statusCode: 500 };
    }
}

interface RequestGangInviteBody {
    gangId: number;
}

export async function requestGangInvite(userId: number, body: RequestGangInviteBody) {
    try {
        const gangId = body.gangId;
        if (!gangId) {
            return { error: "No gang ID provided", statusCode: 400 };
        }

        const gang = await GangRepository.findGangById(gangId);
        if (!gang) {
            return { error: "Gang not found", statusCode: 404 };
        }

        const user = await UserRepository.getUserById(userId);
        if (user && user.gangId) {
            return { error: "You are already in a gang", statusCode: 400 };
        }

        const existingRequest = await GangRepository.findGangInvite(gangId, userId, "inviteRequest");
        if (existingRequest) {
            return { error: "You have already requested to join this gang", statusCode: 400 };
        }

        const existingInvite = await GangRepository.findGangInvite(gangId, userId, "invite");
        if (existingInvite) {
            return { error: "You already have an invite from this gang", statusCode: 400 };
        }

        await GangRepository.createGangInvite({
            gang: { connect: { id: gangId } },
            user_gang_invite_senderIdTouser: { connect: { id: userId } },
            user_gang_invite_recipientIdTouser: { connect: { id: gang.ownerId } },
            inviteType: "inviteRequest",
        });

        NotificationService.NotifyUser(gang.ownerId, NotificationTypes.gang_invite_request, {
            gangId: gangId,
            gangName: gang.name,
            senderId: userId,
            senderName: user?.username,
        });

        return { data: "Request sent" };
    } catch (error) {
        return { error: "Failed to send request", statusCode: 500 };
    }
}

async function validateGangPermissionsAndUpdate(
    userId: number,
    updates: Prisma.gangUpdateInput
): Promise<{ data?: GangModel; error?: string; statusCode?: number }> {
    const user = await GangRepository.findUserWithGangId(userId);
    if (!user || !user.gangId) {
        return { error: "You are not in a gang", statusCode: 400 };
    }
    const gangId = user.gangId;

    const userGangMember = await GangRepository.findGangMember(userId, gangId);
    if (!userGangMember) {
        return { error: "You are not a member of this gang", statusCode: 400 };
    }

    if (userGangMember.rank < 5) {
        return { error: "You don't have permission to update gang info", statusCode: 400 };
    }

    const gang = await GangRepository.findGangById(gangId);
    if (!gang) {
        return { error: "Gang not found", statusCode: 404 };
    }

    const updatedGang = await GangRepository.updateGangResources(gang, updates);
    GangHelper.logGangAction(gangId, "InfoUpdated", "Gang information updated", userId, null);

    return { data: updatedGang };
}

export async function updateGangInfo(
    userId: number,
    body: Prisma.gangUpdateInput,
    files: {
        gang_avatar?: Express.Multer.File[];
    }
) {
    try {
        const updates: Prisma.gangUpdateInput = {};

        if (body.about) {
            updates.about = body.about;
        }

        // Handle avatar upload for Express.Multer.File format
        if (files && files.gang_avatar && files.gang_avatar.length > 0) {
            // Get gang info for avatar processing
            const user = await GangRepository.findUserWithGangId(userId);
            if (user?.gangId) {
                const gang = await GangRepository.findGangById(user.gangId);
                if (gang) {
                    const avatarFile = files.gang_avatar[0];
                    const imageData = {
                        buffer: avatarFile.buffer,
                        mimetype: avatarFile.mimetype,
                    };

                    const avatarUrl = await imagesHelper.saveAsWebp(
                        imageData,
                        imagesHelper.UploadType.GANG_AVATAR,
                        gang.avatar ?? undefined
                    );
                    if (avatarUrl) {
                        updates.avatar = avatarUrl;
                    }
                }
            }
        }

        return await validateGangPermissionsAndUpdate(userId, updates);
    } catch (error) {
        return { error: "Failed to update gang info", statusCode: 500 };
    }
}

// ORPC handler for updating gang info
export async function updateGangInfoORPC(
    userId: number,
    input: {
        about?: string;
        motd?: string;
        gang_avatar?: File;
    }
) {
    try {
        const updates: Prisma.gangUpdateInput = {};

        if (input.about) {
            updates.about = input.about;
        }

        // TODO: Add gang motd
        // if (input.motd) {
        //     updates.motd = input.motd;
        // }

        // Handle avatar upload for File format (ORPC)
        if (input.gang_avatar) {
            // Get gang info for avatar processing
            const user = await GangRepository.findUserWithGangId(userId);
            if (user?.gangId) {
                const gang = await GangRepository.findGangById(user.gangId);
                if (gang) {
                    const buffer = Buffer.from(await input.gang_avatar.arrayBuffer());
                    const imageData: imagesHelper.ImageData = {
                        buffer: buffer,
                        mimetype: input.gang_avatar.type,
                        originalname: input.gang_avatar.name,
                        size: input.gang_avatar.size,
                    };

                    const avatarUrl = await imagesHelper.saveAsWebp(
                        imageData,
                        imagesHelper.UploadType.GANG_AVATAR,
                        gang.avatar ?? undefined
                    );
                    if (avatarUrl) {
                        updates.avatar = avatarUrl;
                    } else {
                        return { error: "Invalid avatar file type", statusCode: 400 };
                    }
                }
            }
        }

        return await validateGangPermissionsAndUpdate(userId, updates);
    } catch (error) {
        const message = error instanceof Error ? error.message : "Something went wrong updating gang info";
        if (
            message.includes("Unsupported file type") ||
            message.includes("Image dimensions too large") ||
            message.includes("File type mismatch")
        ) {
            return { error: message, statusCode: 400 };
        }
        return { error: "Failed to update gang info", statusCode: 500 };
    }
}
